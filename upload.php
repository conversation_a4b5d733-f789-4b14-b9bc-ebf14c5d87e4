<?php

require_once __DIR__ . '/config.php';
session_start();
if (!isset($_SESSION['username'])) {
    header('Location: index.php');
    exit();
}

require_once 'vendor/autoload.php';
use PhpOffice\PhpSpreadsheet\IOFactory;

/**
 * Format a date to the desired display format
 *
 * @param string|DateTime $date The date to format
 * @return string The formatted date string (MM/DD/YYYY HH:MM:SS AM/PM)
 */
function format_display_date($date) {
    if ($date instanceof DateTime) {
        return $date->format('m/d/Y h:i:s A');
    } elseif (is_string($date) && !empty($date)) {
        $date_obj = new DateTime($date);
        return $date_obj->format('m/d/Y h:i:s A');
    }
    return '';
}

require_once 'DatabaseInteraction.php';
require_once 'ScaleConfigurationManager.php';
$db = new DatabaseInteraction();
$conn = $db->connect();
$user_id = $_SESSION['user_id'];

// Fetch previously uploaded data_ids by the logged-in user
$uploadedData = $db->getUploadedDataIdsByUser($user_id);

$upload_folder = 'uploads/';
if (!is_dir($upload_folder)) {
    mkdir($upload_folder, 0777, true);
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['file'])) {
    // Generate data_id first for scale validation
    $data_id = uniqid();

    // MANDATORY SCALE VALIDATION - Must be done BEFORE file processing
    $scaleManager = new ScaleConfigurationManager($conn);
    $scale_validation = $scaleManager->processUserScaleInput($_POST, $data_id, $user_id);

    if (!$scale_validation['success']) {
        $error = 'Scale Configuration Error: ' . implode(', ', $scale_validation['errors']);
        goto skip_processing; // Skip file processing if scale validation fails
    }

    $file = $_FILES['file'];
    $file_path = $upload_folder . basename($file['name']);

    if (move_uploaded_file($file['tmp_name'], $file_path)) {
        try {
            $spreadsheet = IOFactory::load($file_path);
            $worksheet = $spreadsheet->getActiveSheet();
            $data = $worksheet->toArray();

            // Find the column indices
            $headerRow = array_map('strtolower', $data[0]);
            $pidIndex = array_search('pid', $headerRow);
            $commentsIndex = array_search('comments', $headerRow);
            $csatIndex = array_search('csat', $headerRow);
            $npsIndex = array_search('nps', $headerRow);

            // Find additional column indices
            $resolutionCommentIndex = array_search('resolution_comment', $headerRow);
            $internalScoresIndex = array_search('internal_scores', $headerRow);
            $feedbackSubmitDateIndex = array_search('feedback_submit_date', $headerRow);
            $lobIndex = array_search('lob', $headerRow);
            $vendorIndex = array_search('vendor', $headerRow);
            $locationIndex = array_search('location', $headerRow);
            $partnerIndex = array_search('partner', $headerRow);
            $dummy1Index = array_search('dummy-1', $headerRow);
            $dummy2Index = array_search('dummy-2', $headerRow);
            $dummy3Index = array_search('dummy-3', $headerRow);
            $dummy4Index = array_search('dummy-4', $headerRow);
            $dummy5Index = array_search('dummy-5', $headerRow);

            // Check for required columns
            if ($pidIndex !== false && $commentsIndex !== false && $csatIndex !== false && $npsIndex !== false) {
                // data_id already generated above for scale validation
                $domain_category = isset($_POST['domain_category']) ? $_POST['domain_category'] : 'Collections'; // Default to 'Collections' if not set
                $selected_main_drivers = isset($_POST['main_drivers']) ? $_POST['main_drivers'] : [];

                // Validate that at least two main drivers are selected
                if (count($selected_main_drivers) < 2) {
                    $error = 'Please select at least two main drivers.';
                    // Don't use break here as we're not in a loop
                    // Just skip the rest of the processing
                    goto skip_processing;
                }

                // Store selected main drivers in session for later use
                $_SESSION['selected_main_drivers'] = $selected_main_drivers;

                // Save selected main drivers to database
                $db->saveDataMainDrivers($data_id, $selected_main_drivers);

                // Iterate through the rows, starting from the second row (index 1)
                for ($i = 1; $i < count($data); $i++) {
                    $pid = $data[$i][$pidIndex];
                    $comments = $data[$i][$commentsIndex];
                    $csat = $data[$i][$csatIndex];
                    $nps = $data[$i][$npsIndex];

                    // Get additional fields if they exist
                    $resolution_comment = $resolutionCommentIndex !== false ? $data[$i][$resolutionCommentIndex] : null;
                    $internal_scores = $internalScoresIndex !== false ? $data[$i][$internalScoresIndex] : null;
                    $feedback_submit_date = $feedbackSubmitDateIndex !== false ? $data[$i][$feedbackSubmitDateIndex] : null;
                    $lob = $lobIndex !== false ? $data[$i][$lobIndex] : null;
                    $vendor = $vendorIndex !== false ? $data[$i][$vendorIndex] : null;
                    $location = $locationIndex !== false ? $data[$i][$locationIndex] : null;
                    $partner = $partnerIndex !== false ? $data[$i][$partnerIndex] : null;
                    $dummy_1 = $dummy1Index !== false ? $data[$i][$dummy1Index] : null;
                    $dummy_2 = $dummy2Index !== false ? $data[$i][$dummy2Index] : null;
                    $dummy_3 = $dummy3Index !== false ? $data[$i][$dummy3Index] : null;
                    $dummy_4 = $dummy4Index !== false ? $data[$i][$dummy4Index] : null;
                    $dummy_5 = $dummy5Index !== false ? $data[$i][$dummy5Index] : null;

                    // Process feedback_submit_date to extract feedback_month and feedback_time
                    $feedback_month = null;
                    $feedback_time = null;
                    if ($feedback_submit_date) {
                        // Log the raw date value for debugging
                        error_log("Raw feedback_submit_date: " . print_r($feedback_submit_date, true) . " (type: " . gettype($feedback_submit_date) . ")");

                        // Handle Excel date/time format which might be a numeric timestamp
                        if (is_numeric($feedback_submit_date)) {
                            // Convert Excel date value to PHP DateTime
                            // Excel dates are number of days since 1900-01-01 (Windows) or 1904-01-01 (Mac)
                            // We'll assume Windows format (most common)
                            $unix_date = ($feedback_submit_date - 25569) * 86400; // 25569 is days between 1900-01-01 and 1970-01-01
                            $date_obj = new DateTime();
                            $date_obj->setTimestamp($unix_date);

                            // Store the date in MySQL datetime format for the database
                            $feedback_submit_date = $date_obj->format('Y-m-d H:i:s');
                            $feedback_month = $date_obj->format('M-Y');
                            $feedback_time = $date_obj->format('h:i:s A');

                            error_log("Converted numeric date: $feedback_submit_date, Month: $feedback_month, Time: $feedback_time");
                        } else {
                            // Try various date formats
                            $date_formats = [
                                'Y-m-d H:i:s',
                                'Y-m-d',
                                'd/m/Y H:i:s',
                                'd/m/Y',
                                'm/d/Y H:i:s',
                                'm/d/Y H:i', // Format like "10/2/2024 7:32"
                                'm/d/Y',
                                'n/j/Y H:i', // Format with no leading zeros like "10/2/2024 7:32"
                                'n/j/Y g:i A' // Format with AM/PM like "10/2/2024 7:32 AM"
                            ];

                            $date_obj = null;

                            // First, clean the date string by removing Excel locale information
                            $cleaned_date_string = preg_replace('/\[\$-.*?\]/', '', $feedback_submit_date);
                            error_log("Cleaned feedback_submit_date: " . $cleaned_date_string);

                            // Try to parse as MM/DD/YYYY h:i:s A (with AM/PM)
                            $format = 'm/d/Y h:i:s A';
                            $date_obj = DateTime::createFromFormat($format, trim($cleaned_date_string));

                            // If that didn't work, try MM/DD/YYYY h:i A (with AM/PM but no seconds)
                            if (!$date_obj) {
                                $format = 'm/d/Y h:i A';
                                $date_obj = DateTime::createFromFormat($format, trim($cleaned_date_string));
                            }

                            // If that didn't work, try MM/DD/YYYY H:i:s (24-hour format)
                            if (!$date_obj) {
                                $format = 'm/d/Y H:i:s';
                                $date_obj = DateTime::createFromFormat($format, trim($cleaned_date_string));
                            }

                            // If that didn't work, try MM/DD/YYYY H:i (24-hour format, no seconds)
                            if (!$date_obj) {
                                $format = 'm/d/Y H:i';
                                $date_obj = DateTime::createFromFormat($format, trim($cleaned_date_string));
                            }

                            // If none of the above worked, try the original regex approach
                            if (!$date_obj && preg_match('/^(\d{1,2})\/(\d{1,2})\/(\d{4})\s+(\d{1,2}):(\d{2})$/', $cleaned_date_string, $matches)) {
                                // Format is m/d/Y H:i
                                $month = $matches[1];
                                $day = $matches[2];
                                $year = $matches[3];
                                $hour = $matches[4];
                                $minute = $matches[5];

                                // Create a properly formatted date string
                                $formatted_date = sprintf('%04d-%02d-%02d %02d:%02d:00', $year, $month, $day, $hour, $minute);
                                error_log("Reformatted date from {$cleaned_date_string} to {$formatted_date}");

                                $format = 'Y-m-d H:i:s';
                                $date_obj = DateTime::createFromFormat($format, $formatted_date);

                                if ($date_obj) {
                                    // Store the date in MySQL datetime format for the database
                                    $feedback_submit_date = $date_obj->format('Y-m-d H:i:s');
                                    $feedback_month = $date_obj->format('M-Y');

                                    // Extract the time portion for the feedback_time field
                                    $feedback_time = $date_obj->format('h:i:s A');

                                    error_log("Converted regex date: MySQL format: $feedback_submit_date, Month: $feedback_month, Time: $feedback_time");
                                }
                            }

                            // If the special handling didn't work, try the standard formats
                            if (!$date_obj) {
                                foreach ($date_formats as $format) {
                                    $date_obj = DateTime::createFromFormat($format, $feedback_submit_date);
                                    if ($date_obj) {
                                        break;
                                    }
                                }
                            }

                            if ($date_obj) {
                                // Store the date in MySQL datetime format for the database
                                $feedback_submit_date = $date_obj->format('Y-m-d H:i:s');
                                $feedback_month = $date_obj->format('M-Y');

                                // Extract the time portion for the feedback_time field
                                $feedback_time = $date_obj->format('h:i:s A');

                                error_log("Converted string date: MySQL format: $feedback_submit_date, Month: $feedback_month, Time: $feedback_time");
                            } else {
                                // If all parsing attempts fail, log the issue
                                error_log("Could not parse date: $feedback_submit_date");
                            }
                        }
                    }

                    // Clean the comments data
                    $cleaned_comments = preg_replace('/[^a-zA-Z0-9\s.,!?]/u', '', $comments);

                    $db->insertFeedbackData(
                        $data_id,
                        $pid,
                        $cleaned_comments,
                        $csat,
                        $nps,
                        $user_id,
                        $domain_category,
                        $resolution_comment,
                        $internal_scores,
                        $feedback_submit_date,
                        $feedback_month,
                        $feedback_time,
                        $lob,
                        $vendor,
                        $location,
                        $partner,
                        $dummy_1,
                        $dummy_2,
                        $dummy_3,
                        $dummy_4,
                        $dummy_5
                    );
                }

                // Trigger the background processing
                $process_url = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) . "/process_background.php?data_id=" . urlencode($data_id);
                error_log("Triggering background processing for data_id: $data_id at URL: $process_url");

                // Try to use cURL for a non-blocking request
                if (function_exists('curl_init')) {
                    $ch = curl_init($process_url);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_TIMEOUT, 1); // Set a very short timeout
                    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 1);
                    curl_setopt($ch, CURLOPT_NOSIGNAL, 1); // Ignore signals (prevents SIGALRM)
                    $response = curl_exec($ch);
                    $curl_error = curl_error($ch);
                    if (!empty($curl_error)) {
                        error_log("cURL error when triggering process_background.php: $curl_error");
                    }
                    curl_close($ch);
                } else {
                    // Fallback to file_get_contents with a timeout context
                    error_log("cURL not available, using file_get_contents instead");
                    $context = stream_context_create([
                        'http' => [
                            'timeout' => 1,  // 1 second timeout
                            'ignore_errors' => true
                        ]
                    ]);
                    @file_get_contents($process_url, false, $context);
                }

                // Redirect to dashboard with processing flag
                header('Location: dashboard.php?data_id=' . urlencode($data_id) . '&processing=true');
                exit();
            } else {
                $error = 'One or more required columns (pid, comments, CSAT, NPS) were not found.';
            }

            skip_processing: // Label for goto statement
        } catch (\PhpOffice\PhpSpreadsheet\Reader\Exception $e) {
            $error = 'Error reading the file: ' . $e->getMessage();
        } catch (\Exception $e) {
            $error = 'An unexpected error occurred: ' . $e->getMessage();
        }
    } else {
        $error = 'File upload failed.';
    }
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="script-src 'self' 'unsafe-inline' https://cdn.segment.com https://cdn.jsdelivr.net https://code.jquery.com">
    <title>VoC Voice of Customer Tool</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="assets/js/domain-drivers.js"></script>
    <script src="assets/js/file-upload.js"></script>
    <style>
        /* Navbar Styles */
        nav {
          background-color: #b98b04; /* Dark brown */
          color: white;
          padding: 5px 20px;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
    </style>
</head>

<body class="bg-gray-100">
    <!-- Navbar -->
    <nav>
        <div class="container mx-auto flex justify-between items-center">
            <div class="logo">
                <img src="assets/images/logo.png" alt="Logo" class="h-full max-h-14 object-contain">
            </div>
            <div class="ml-10 flex items-baseline space-x-4 nav-right">
                <span class="text-white">Hello, <?php echo htmlspecialchars($_SESSION['username']); ?></span>
                <a href="dashboard.php" class="text-white hover:text-indigo-300">Dashboard</a>
                <a href="logout.php" class="text-white hover:text-indigo-300">Logout</a>
            </div>
        </div>
    </nav>
    <div class="container mx-auto py-2">
        <div class="flex justify-center items-center w-full max-w-6xl mx-auto">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-2">
                <div class="feature bg-yellow-50 p-6 rounded-lg shadow-md text-center">
                    <img src="assets/images/summary.png" alt="Sentiment Analysis" class="w-12 h-12 mx-auto mb-4">
                    <p class="text-gray-600">Get comprehensive overview about all of your customers sentiment.</p>
                </div>
                <div class="feature bg-yellow-50 p-6 rounded-lg shadow-md text-center">
                    <img src="assets/images/ai.png" alt="AI Analysis" class="w-12 h-12 mx-auto mb-4">
                    <p class="text-gray-600">Identify the root causes of the negative sentiment of your customers immediately.</p>
                </div>
                <div class="feature bg-yellow-50 p-6 rounded-lg shadow-md text-center">
                    <img src="assets/images/idea.png" alt="Insights" class="w-12 h-12 mx-auto mb-4">
                    <p class="text-gray-600">Get actionable insights in just one click and delve deeper into what causes poor customer experience.</p>
                </div>
            </div>
        </div>
    </div>

    <div class="container mx-auto py-2">
	<div class="bg-yellow-50 p-2 rounded-lg shadow-md max-w-6xl mx-auto">
    <h1 class="text-3xl font-bold mb-6 text-center">(VoC) Voice of Customer Tool</h1>
    <p>🚀 Get insight about the Voice of your Customer immediately</p>
    <p>
        Easily uncover the <b>root cause of your customer issues</b>, delve into their <b>underlying sentiment</b>,
        harness these insights to <b>identify gaps in your business</b>, and <b>formulate actionable strategies</b>
        with our Voice of Customer Tool.
    </p>
    <p class="format-note">
        <strong style="color: red;">Please download and follow the sample file format before uploading.</strong>

		<div class="flex flex-wrap gap-2 mt-2">
            <a href="assets/images/sample-upload-file-new.xlsx" class="sample-download bg-yellow-100 hover:bg-yellow-200 px-3 py-1 rounded flex items-center">
                Download Sample File
                <img src="assets/images/xlsx.png" alt="Excel Workbook Icon" style="width: 24px; height: 24px; display: inline-block; vertical-align: middle; margin-left: 5px;">
            </a>
            <a href="view_instructions.php?file=UPLOAD_INSTRUCTIONS.md" class="sample-download bg-yellow-100 hover:bg-yellow-200 px-3 py-1 rounded flex items-center">
                Instructions: How to Upload VOC Data
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
            </a>
        </div>
    </p>
	    <div class="mt-6 p-4 bg-yellow-50 rounded-lg shadow">
            <h3 class="text-lg font-semibold mb-4">Select Domain Category</h3>
            <div class="mb-4">
                <label for="domain_category" class="block text-sm font-medium text-gray-700 mb-2">Domain Category:</label>
                <select id="domain_category" name="domain_category" class="w-full px-3 py-2 bg-yellow-100 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required>
                    <option value="">Select a domain category</option>
                    <?php
                    $domain_categories = $db->getAllDomainCategories();
                    foreach ($domain_categories as $category) {
                        echo "<option value=\"" . htmlspecialchars($category) . "\">" . htmlspecialchars($category) . "</option>";
                    }
                    ?>
                </select>
            </div>

            <div class="mb-4">
                <h4 class="text-md font-semibold mb-2">Main Drivers: <span class="text-sm text-red-500">(Select at least 2)</span></h4>
                <div id="main_drivers_container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    <p class="text-gray-500 col-span-full">Please select a domain category first</p>
                </div>
                <p class="text-xs text-gray-500 mt-2">Note: You must select at least two main drivers to proceed with the upload.</p>
            </div>
        </div>

    <p class="text-gray-500 mb-6">Upload your file here 👇</p>
    <form id="uploadForm" action="upload.php" method="post" enctype="multipart/form-data" class="mb-6">

        <!-- MANDATORY SCALE CONFIGURATION SECTION -->
        <div id="scaleConfigSection" class="mt-6 p-4 bg-red-50 border-2 border-red-200 rounded-lg shadow" style="display: none;">
            <h3 class="text-lg font-semibold mb-4 text-red-700">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                Scale Configuration (Required)
            </h3>
            <p class="text-sm text-red-600 mb-4">
                <strong>All three scale configurations are mandatory for accurate calculations.</strong>
                Please specify the scale ranges used in your data before uploading.
            </p>

            <!-- CSAT Scale Configuration -->
            <div class="mb-4 p-3 bg-white rounded border">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    <span class="text-red-500">*</span> CSAT Scale Range
                </label>
                <select name="csat_scale_type" id="csat_scale_type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500" required>
                    <option value="">Select CSAT Scale (Required)</option>
                    <option value="1-5">1-5 (Very Dissatisfied to Very Satisfied)</option>
                    <option value="1-7">1-7 (7-point Likert Scale)</option>
                    <option value="1-10">1-10 (10-point Scale)</option>
                    <option value="custom">Custom Range</option>
                </select>

                <!-- Custom CSAT Range -->
                <div id="csat_custom_range" class="mt-2 grid grid-cols-2 gap-2" style="display:none;">
                    <input type="number" name="csat_min" placeholder="Min value" min="0" max="100" class="px-3 py-2 border border-gray-300 rounded-md">
                    <input type="number" name="csat_max" placeholder="Max value" min="1" max="100" class="px-3 py-2 border border-gray-300 rounded-md">
                </div>
                <div id="csat_preview" class="mt-2 text-sm text-gray-600"></div>
            </div>

            <!-- NPS Scale Configuration -->
            <div class="mb-4 p-3 bg-white rounded border">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    <span class="text-red-500">*</span> NPS Scale Range
                </label>
                <select name="nps_scale_type" id="nps_scale_type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500" required>
                    <option value="">Select NPS Scale (Required)</option>
                    <option value="0-10">0-10 (Traditional NPS)</option>
                    <option value="1-10">1-10 (Modified NPS)</option>
                    <option value="1-5">1-5 (5-point NPS)</option>
                    <option value="1-7">1-7 (7-point NPS)</option>
                    <option value="custom">Custom Range</option>
                </select>

                <!-- Custom NPS Range -->
                <div id="nps_custom_range" class="mt-2 grid grid-cols-2 gap-2" style="display:none;">
                    <input type="number" name="nps_min" placeholder="Min value" min="0" max="100" class="px-3 py-2 border border-gray-300 rounded-md">
                    <input type="number" name="nps_max" placeholder="Max value" min="1" max="100" class="px-3 py-2 border border-gray-300 rounded-md">
                </div>
                <div id="nps_preview" class="mt-2 text-sm text-gray-600"></div>
            </div>

            <!-- Internal Score Scale Configuration -->
            <div class="mb-4 p-3 bg-white rounded border">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    <span class="text-red-500">*</span> Internal Score Scale Range
                </label>
                <select name="internal_score_scale_type" id="internal_score_scale_type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500" required>
                    <option value="">Select Internal Score Scale (Required)</option>
                    <option value="not_used">Not Used (No Internal Score in data)</option>
                    <option value="1-5">1-5</option>
                    <option value="1-10">1-10</option>
                    <option value="0-100">0-100 (Percentage)</option>
                    <option value="custom">Custom Range</option>
                </select>

                <!-- Custom Internal Score Range -->
                <div id="internal_score_custom_range" class="mt-2 grid grid-cols-2 gap-2" style="display:none;">
                    <input type="number" name="internal_score_min" placeholder="Min value" class="px-3 py-2 border border-gray-300 rounded-md">
                    <input type="number" name="internal_score_max" placeholder="Max value" class="px-3 py-2 border border-gray-300 rounded-md">
                </div>
                <div id="internal_score_preview" class="mt-2 text-sm text-gray-600"></div>
            </div>

            <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
                <p class="text-sm text-yellow-800">
                    <i class="fas fa-info-circle mr-1"></i>
                    <strong>Important:</strong> These scale configurations will be used for all calculations including NPS promoters/detractors classification and CSAT baseline calculations.
                </p>
            </div>
        </div>
        <!-- Hidden input fields for domain_category and main_drivers -->
        <input type="hidden" id="domain_category_input" name="domain_category" value="">
        <div id="main_drivers_hidden_inputs"></div>

        <div id="uploadBox" class="border-2 border-dashed border-gray-300 p-6 rounded-lg text-center">
            <p class="text-gray-500">📎 Attach file</p>
            <p id="fileName" class="text-gray-500">There is nothing attached.</p>
            <input type="file" id="fileInput" name="file" accept=".xlsx" style="display: none;">
            <button type="button" id="chooseFileBtn" class="bg-yellow-100 text-black font-bold py-2 px-4 rounded hover:bg-yellow-400">Choose file</button>
        </div>

        <div class="flex justify-center mt-4">
            <button type="submit" id="uploadButton" class="bg-gray-300 text-gray-500 font-bold py-2 px-4 rounded cursor-not-allowed" disabled>
                Upload (Complete all required fields)
            </button>
        </div>
    </form>


    <?php if (isset($error)): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
            <span class="block sm:inline"><?php echo htmlspecialchars($error); ?></span>
        </div>
    <?php endif; ?>
</div>
            <?php if (!empty($uploadedData)): ?>
                <hr class="my-4 border-b">
				<div class="bg-yellow-50 p-1 rounded-lg shadow-md max-w-6xl mx-auto">
                <h3 class="text-lg font-semibold mb-4">Previously Uploaded Data</h3>
                <div class="overflow-x-auto ">
                    <table class="min-w-full bg-white border border-gray-200 text-sm">
                        <thead class="bg-yellow-100">
                            <tr>
                                <th class="px-4 py-2 border-b text-left">Data ID</th>
                                <th class="px-4 py-2 border-b text-left">Uploaded At</th>
                                <th class="px-4 py-2 border-b text-left">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($uploadedData as $data): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-4 py-2 border-b"><?php echo htmlspecialchars($data['data_id']); ?></td>
                                    <td class="px-4 py-2 border-b"><?php echo htmlspecialchars($data['uploaded_at']); ?></td>
                                    <td class="px-4 py-2 border-b">
                                        <a href="summarize.php?data_id=<?php echo urlencode($data['data_id']); ?>" class="text-yellow-600 hover:text-indigo-700">View Summary</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <p class="text-gray-500 mt-4">No previous uploads found.</p>
            <?php endif; ?>
        </div>
		</div>
    </div>


    <div>
        <footer class="bg-yellow-300 py-2 shadow-md mt-auto">
            <div class="container mx-auto px-9 flex justify-between items-center">
                <div>
                    <p class="text-gray-700 text-xs"><b>&#169 <?php echo date("Y"); ?> Bill Gosling Outsourcing. All rights reserved.</b></p>
                    <p class="text-gray-700 text-xs">This system is for the use of authorized personnel only and by accessing this system you hereby consent to the system being monitored by the Company. Any unauthorized use will be considered a breach of the Company's Information Security policies and may also be unlawful under law. Bill Gosling Outsourcing reserves the right to take any action including disciplinary action or legal proceedings in a court of law against persons involved in the violation of the access restrictions herein. The Information Is 'Internal, Restricted'.</p>
                </div>

            </div>
        </footer>
    </div>

    <!-- Scale Configuration JavaScript -->
    <script>
        $(document).ready(function() {
            // Show scale configuration section when domain category and main drivers are selected
            function checkScaleConfigVisibility() {
                const domainCategory = $('#domain_category').val();
                const mainDriversSelected = $('input[name="main_drivers[]"]:checked').length;

                if (domainCategory && mainDriversSelected >= 2) {
                    $('#scaleConfigSection').slideDown();
                } else {
                    $('#scaleConfigSection').slideUp();
                }
                validateForm();
            }

            // Make the function globally available
            window.checkScaleConfigVisibility = checkScaleConfigVisibility;

            // Handle custom range visibility
            $('#csat_scale_type').on('change', function() {
                const customDiv = $('#csat_custom_range');
                if (this.value === 'custom') {
                    customDiv.slideDown();
                } else {
                    customDiv.slideUp();
                }
                updatePreview('csat');
                validateForm();
            });

            $('#nps_scale_type').on('change', function() {
                const customDiv = $('#nps_custom_range');
                if (this.value === 'custom') {
                    customDiv.slideDown();
                } else {
                    customDiv.slideUp();
                }
                updatePreview('nps');
                validateForm();
            });

            $('#internal_score_scale_type').on('change', function() {
                const customDiv = $('#internal_score_custom_range');
                if (this.value === 'custom') {
                    customDiv.slideDown();
                } else {
                    customDiv.slideUp();
                }
                updatePreview('internal_score');
                validateForm();
            });

            // Handle custom range input changes
            $('input[name="csat_min"], input[name="csat_max"]').on('input', function() {
                updatePreview('csat');
                validateForm();
            });

            $('input[name="nps_min"], input[name="nps_max"]').on('input', function() {
                updatePreview('nps');
                validateForm();
            });

            $('input[name="internal_score_min"], input[name="internal_score_max"]').on('input', function() {
                updatePreview('internal_score');
                validateForm();
            });

            // Form validation
            function validateForm() {
                const domainCategory = $('#domain_category').val();
                const mainDrivers = $('input[name="main_drivers[]"]:checked').length;
                const fileSelected = $('#fileInput')[0].files.length > 0;
                const csatScale = $('#csat_scale_type').val();
                const npsScale = $('#nps_scale_type').val();
                const internalScoreScale = $('#internal_score_scale_type').val();

                // Check if scale configuration section should be visible
                const scaleConfigRequired = domainCategory && mainDrivers >= 2;

                // Check custom ranges if selected
                let csatValid = true;
                let npsValid = true;
                let internalScoreValid = true;

                if (csatScale === 'custom') {
                    const csatMin = $('input[name="csat_min"]').val();
                    const csatMax = $('input[name="csat_max"]').val();
                    csatValid = csatMin && csatMax && parseInt(csatMin) < parseInt(csatMax);
                }

                if (npsScale === 'custom') {
                    const npsMin = $('input[name="nps_min"]').val();
                    const npsMax = $('input[name="nps_max"]').val();
                    npsValid = npsMin && npsMax && parseInt(npsMin) < parseInt(npsMax);
                }

                if (internalScoreScale === 'custom') {
                    const internalMin = $('input[name="internal_score_min"]').val();
                    const internalMax = $('input[name="internal_score_max"]').val();
                    internalScoreValid = internalMin && internalMax && parseInt(internalMin) < parseInt(internalMax);
                }

                // Determine what's required based on current state
                let allValid = domainCategory && mainDrivers >= 2;

                // If scale configuration is required (visible), validate it
                if (scaleConfigRequired) {
                    allValid = allValid && csatScale && npsScale && internalScoreScale &&
                              csatValid && npsValid && internalScoreValid;
                }

                // File is always required at the end
                allValid = allValid && fileSelected;

                const uploadButton = $('#uploadButton');
                if (allValid) {
                    uploadButton.prop('disabled', false)
                               .removeClass('bg-gray-300 text-gray-500 cursor-not-allowed')
                               .addClass('bg-yellow-100 hover:bg-yellow-400 text-black cursor-pointer')
                               .text('Upload');
                } else {
                    uploadButton.prop('disabled', true)
                               .removeClass('bg-yellow-100 hover:bg-yellow-400 text-black cursor-pointer')
                               .addClass('bg-gray-300 text-gray-500 cursor-not-allowed');

                    // Update button text based on what's missing
                    if (!domainCategory) {
                        uploadButton.text('Select Domain Category');
                    } else if (mainDrivers < 2) {
                        uploadButton.text('Select at least 2 Main Drivers');
                    } else if (scaleConfigRequired && (!csatScale || !npsScale || !internalScoreScale)) {
                        uploadButton.text('Complete Scale Configuration');
                    } else if (scaleConfigRequired && (!csatValid || !npsValid || !internalScoreValid)) {
                        uploadButton.text('Fix Scale Configuration Errors');
                    } else if (!fileSelected) {
                        uploadButton.text('Select File to Upload');
                    } else {
                        uploadButton.text('Complete all required fields');
                    }
                }
            }

            // Update preview text for scale configurations
            function updatePreview(scaleType) {
                const scaleValue = $(`#${scaleType}_scale_type`).val();
                const previewDiv = $(`#${scaleType}_preview`);

                if (!scaleValue) {
                    previewDiv.text('');
                    return;
                }

                let previewText = '';

                if (scaleType === 'csat') {
                    if (scaleValue === 'custom') {
                        const min = $('input[name="csat_min"]').val();
                        const max = $('input[name="csat_max"]').val();
                        if (min && max) {
                            const baseline = Math.round((parseInt(min) + (parseInt(max) - parseInt(min)) * 0.8) * 100) / 100;
                            previewText = `Custom range: ${min}-${max}, Baseline: ${baseline}`;
                        }
                    } else {
                        const ranges = {'1-5': [1,5,4], '1-7': [1,7,5.6], '1-10': [1,10,8]};
                        if (ranges[scaleValue]) {
                            previewText = `Range: ${ranges[scaleValue][0]}-${ranges[scaleValue][1]}, Baseline: ${ranges[scaleValue][2]}`;
                        }
                    }
                } else if (scaleType === 'nps') {
                    if (scaleValue === 'custom') {
                        const min = $('input[name="nps_min"]').val();
                        const max = $('input[name="nps_max"]').val();
                        if (min && max) {
                            const promoter = parseInt(max) - 1;
                            const detractor = parseInt(min) + Math.floor((parseInt(max) - parseInt(min)) * 0.4);
                            previewText = `Custom range: ${min}-${max}, Promoters: ${promoter}-${max}, Detractors: ${min}-${detractor}`;
                        }
                    } else {
                        const thresholds = {
                            '0-10': 'Promoters: 9-10, Passives: 7-8, Detractors: 0-6',
                            '1-10': 'Promoters: 9-10, Passives: 7-8, Detractors: 1-6',
                            '1-5': 'Promoters: 4-5, Passives: 3, Detractors: 1-2',
                            '1-7': 'Promoters: 6-7, Passives: 4-5, Detractors: 1-3'
                        };
                        previewText = thresholds[scaleValue] || '';
                    }
                } else if (scaleType === 'internal_score') {
                    if (scaleValue === 'not_used') {
                        previewText = 'Internal scores will not be used in calculations';
                    } else if (scaleValue === 'custom') {
                        const min = $('input[name="internal_score_min"]').val();
                        const max = $('input[name="internal_score_max"]').val();
                        if (min && max) {
                            previewText = `Custom range: ${min}-${max}`;
                        }
                    } else {
                        previewText = `Range: ${scaleValue}`;
                    }
                }

                previewDiv.text(previewText);
            }

            // Trigger validation on existing form elements
            $('#domain_category').on('change', function() {
                // Add a delay to allow the main drivers to be loaded and selected
                setTimeout(function() {
                    checkScaleConfigVisibility();
                    validateForm();
                }, 500);
            });
            $(document).on('change', 'input[name="main_drivers[]"]', function() {
                checkScaleConfigVisibility();
                validateForm();
            });

            // File input change event
            $('#fileInput').on('change', function() {
                const fileInput = this;
                const fileName = $('#fileName');

                if (fileInput.files.length > 0) {
                    fileName.text(fileInput.files[0].name);
                } else {
                    fileName.text('There is nothing attached.');
                }

                validateForm();
            });

            // Initial validation
            validateForm();
        });
    </script>
</body>
</html>
