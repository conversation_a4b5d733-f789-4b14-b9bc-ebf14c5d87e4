# Feedback Upload Instructions

## New Upload Process

The feedback upload system has been updated with the following enhancements:

1. **Domain Category Selection**: You can now select which domain category your feedback data belongs to.
2. **Main Driver Selection**: You can select which main drivers are relevant to your dataset.
3. **Scale Configuration**: You must configure the scale ranges used in your data for accurate calculations.
4. **Additional Fields**: The system now supports additional fields for more comprehensive feedback analysis.

## Required and Optional Fields

### Required Fields
These fields must be present in your upload file:
- `pid` - Participant/Customer ID
- `comments` - Feedback text
- `CSAT` - Customer Satisfaction score
- `NPS` - Net Promoter Score
- `feedback_submit_date` - Date and time when feedback was submitted (format: YYYY-MM-DD HH:MM:SS)

### Optional Fields
These fields can be included for more detailed analysis:
- `resolution_comment` - Comments about how the issue was resolved
- `internal_scores` - Internal scoring metrics
- `lob` - Line of Business
- `vendor` - Vendor information
- `location` - Location information
- `partner` - Partner information
- `dummy-1` through `dummy-5` - Additional custom fields for your specific needs

## Upload Process

1. **Prepare Your Data**: Use the provided `sample_upload_template.xlsx` as a starting point. You can convert it to Excel format if needed.

2. **Upload Your File**:
   - Log in to the system
   - You will be directed to the upload page
   - **Step 1**: Select the appropriate Domain Category from the dropdown
   - **Step 2**: Select relevant Main Drivers for your dataset (at least 2 required, all are selected by default)
   - **Step 3**: Configure Scale Ranges (appears automatically after selecting domain category and drivers)
   - **Step 4**: Attach your Excel file
   - **Step 5**: Click "Upload"

3. **Domain Categories and Main Drivers**:

   | Domain Category | Main Drivers |
   |-----------------|-------------|
   | Collections | Billing & Payment Issues, Customer Service & Resolution Issues, Customer Support & Service, Policy & Procedures, Tools & Technology, Transfer & Process Issues |
   | Customer Service | Agent Behavior, Call Quality Issues, Communication Clarity, First Call Resolution, Knowledge Management, Resolution Efficiency |
   | Finance & Banking | Account Access Issues, Account Application, Billing & Payment Issues, Customer Service & Resolution Issues, Dispute Handling, Loan & Credit Issues, Policy & Procedures, Refund Delays, Refund Policies & Eligibility, Tools & Technology, Transaction Errors, Verification Issues |
   | Retail & E-commerce | Customer Service, Delivery Experience, Order Fulfillment, Product Quality, Returns & Refunds, Website Experience |
   | Sales & Marketing | Brand Representation, Customer Service & Resolution Issues, Customer Support & Service, Follow-up Communication, Lack of Clear Communication, Lead Handling, Marketing, Perceived Company Integrity, Product Knowledge Gaps, Promotions & Discounts |
   | Technical Support | Hardware Issues, Self-Service Portal Issues, Software Problems, System Downtime, Tool Integration, User Accessibility |

4. **Scale Configuration (Mandatory)**:

   After selecting your domain category and main drivers, you **must** configure the scale ranges used in your data. This section will appear automatically and includes:

   **CSAT Scale Range** (Required):
   - 1-5 (Very Dissatisfied to Very Satisfied)
   - 1-7 (7-point Likert Scale)
   - 1-10 (10-point Scale) - *Most common*
   - Custom Range (specify your own min/max values)

   **NPS Scale Range** (Required):
   - 0-10 (Traditional NPS) - *Most common*
   - 1-10 (Modified NPS)
   - 1-5 (5-point NPS)
   - 1-7 (7-point NPS)
   - Custom Range (specify your own min/max values)

   **Internal Score Scale Range** (Required):
   - Not Used (No Internal Score in data) - *Select this if your data doesn't include internal scores*
   - 1-5
   - 1-10
   - 0-100 (Percentage)
   - Custom Range (specify your own min/max values)

   **Important Notes**:
   - All three scale configurations are mandatory for accurate calculations
   - These settings determine how NPS promoters/detractors are classified
   - CSAT baseline calculations depend on your scale configuration
   - If you don't have internal scores in your data, select "Not Used"
   - The system will show preview information for each scale configuration

5. **Processing**:
   - The system will process your data using the selected domain category, main drivers, and scale configurations
   - You will be redirected to the summary page once processing is complete

## Notes

- The system will automatically extract `feedback_month` (MMM-YYYY format) and `feedback_time` (HH:MM:SS format) from the `feedback_submit_date` field if provided.
- If time information is not available in the `feedback_submit_date`, the `feedback_time` will be set to "NA".
- All main drivers for the selected domain category will be available for selection.
- You can use the "Select All" checkbox to quickly select all main drivers for the chosen domain category.

## Sample File

A sample upload template (`sample_upload_template.xlsx`) has been provided in the root directory. You can use this as a starting point for your data uploads.
