document.addEventListener("DOMContentLoaded", function() {
    // Ensure the file input is correctly triggered
    const chooseFileBtn = document.getElementById("chooseFileBtn");
    if (chooseFileBtn) {
        chooseFileBtn.addEventListener("click", function (event) {
            event.preventDefault();
            event.stopPropagation();
            document.getElementById('fileInput').click();
        });
    }

    // File input change handling is now done in upload.php to avoid conflicts

    const uploadForm = document.getElementById("uploadForm");
    if (uploadForm) {
        uploadForm.addEventListener("submit", function (event) {
            // Make sure domain category is selected
            const domainCategory = document.getElementById('domain_category');
            if (!domainCategory || !domainCategory.value) {
                event.preventDefault();
                alert("Please select a domain category before uploading.");
                return false;
            }

            // Check if at least two main drivers are selected
            const mainDrivers = document.querySelectorAll('input[name="main_drivers[]"]:checked');
            if (mainDrivers.length < 2) {
                event.preventDefault();
                alert("Please select at least two main drivers before uploading.");
                return false;
            }

            return true;
        });
    }
});
